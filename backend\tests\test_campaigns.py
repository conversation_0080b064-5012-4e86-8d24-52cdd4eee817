# tests/test_campaigns.py
"""
Test suite for the Campaigns application

This module contains comprehensive tests for the campaigns app functionality,
including CRUD operations, security, validation, and business logic.
"""

from rest_framework.test import APITestCase
from django.urls import reverse
from rest_framework import status
from users.models import HRManager
from campaigns.models import Campaign
from django.contrib.auth.hashers import make_password
from datetime import date, timedelta


class CampaignModelTest(APITestCase):
    """Test cases for Campaign model validation and business logic"""
    
    def setUp(self):
        self.hr_manager = HRManager.objects.create(
            name='Test HR Manager',
            email='<EMAIL>',
            password_hash=make_password('password123'),
            company_name='Test Company'
        )

    def test_campaign_creation_success(self):
        """Test successful campaign creation with valid data"""
        campaign = Campaign.objects.create(
            title="Test Campaign",
            description="Test Description",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30),
            hr_manager=self.hr_manager
        )
        
        self.assertEqual(campaign.title, "Test Campaign")
        self.assertEqual(campaign.hr_manager, self.hr_manager)
        self.assertTrue(campaign.created_at)

    def test_campaign_date_validation(self):
        """Test that end_date must be after start_date"""
        from django.core.exceptions import ValidationError
        
        campaign = Campaign(
            title="Invalid Campaign",
            start_date=date.today() + timedelta(days=30),
            end_date=date.today() + timedelta(days=1),  # end_date < start_date
            hr_manager=self.hr_manager
        )
        
        with self.assertRaises(ValidationError):
            campaign.full_clean()

    def test_campaign_date_modification_protection(self):
        """Test that dates cannot be modified after creation"""
        from django.core.exceptions import ValidationError
        
        campaign = Campaign.objects.create(
            title="Test Campaign",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30),
            hr_manager=self.hr_manager
        )
        
        # Try to modify dates
        campaign.start_date = date.today() + timedelta(days=5)
        
        with self.assertRaises(ValidationError):
            campaign.save()


class CampaignAPITest(APITestCase):
    """Test cases for Campaign API endpoints and CRUD operations"""
    
    def setUp(self):
        # Create two HR managers for testing security
        self.hr_manager1 = HRManager.objects.create(
            name='HR Manager 1',
            email='<EMAIL>',
            password_hash=make_password('password123'),
            company_name='Company 1'
        )
        
        self.hr_manager2 = HRManager.objects.create(
            name='HR Manager 2',
            email='<EMAIL>',
            password_hash=make_password('password123'),
            company_name='Company 2'
        )

        # Test data
        self.valid_campaign_data = {
            "title": "Test Campaign",
            "description": "Test Description",
            "start_date": (date.today() + timedelta(days=1)).isoformat(),
            "end_date": (date.today() + timedelta(days=30)).isoformat()
        }
        
        self.invalid_campaign_data = {
            "title": "Invalid Campaign",
            "description": "Test Description",
            "start_date": (date.today() + timedelta(days=30)).isoformat(),
            "end_date": (date.today() + timedelta(days=1)).isoformat()  # Invalid dates
        }

    def authenticate_user(self, user):
        """Helper method to authenticate a user"""
        self.client.force_authenticate(user=user)

    def test_create_campaign_success(self):
        """Test successful campaign creation via API"""
        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-list')
        response = self.client.post(url, self.valid_campaign_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Campaign.objects.count(), 1)
        campaign = Campaign.objects.get()
        self.assertEqual(campaign.hr_manager, self.hr_manager1)
        self.assertEqual(campaign.title, "Test Campaign")

    def test_create_campaign_invalid_dates(self):
        """Test campaign creation with invalid dates"""
        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-list')
        response = self.client.post(url, self.invalid_campaign_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('end_date', response.data)

    def test_create_campaign_unauthenticated(self):
        """Test that unauthenticated users cannot create campaigns"""
        url = reverse('campaign-list')
        response = self.client.post(url, self.valid_campaign_data, format='json')

        # Peut être 401 ou 403 selon la configuration des permissions
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_list_campaigns_filtered_by_owner(self):
        """Test that each HR manager only sees their own campaigns"""
        # Create campaigns for each HR manager
        Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Campaign HR1",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )
        
        Campaign.objects.create(
            hr_manager=self.hr_manager2,
            title="Campaign HR2",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )

        # Test with HR Manager 1
        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], "Campaign HR1")

        # Test with HR Manager 2
        self.authenticate_user(self.hr_manager2)
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['title'], "Campaign HR2")

    def test_update_campaign_dates_protection(self):
        """Test that campaign dates cannot be modified via API"""
        campaign = Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Test Campaign",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )

        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-detail', args=[campaign.id])

        # Attempt to modify dates (should be ignored)
        update_data = {
            "title": "Updated Title",
            "start_date": (date.today() + timedelta(days=5)).isoformat(),
            "end_date": (date.today() + timedelta(days=35)).isoformat()
        }

        response = self.client.patch(url, update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        campaign.refresh_from_db()
        self.assertEqual(campaign.title, "Updated Title")  # Title updated
        # Dates should remain unchanged
        self.assertEqual(campaign.start_date, date.today() + timedelta(days=1))
        self.assertEqual(campaign.end_date, date.today() + timedelta(days=30))

    def test_update_campaign_title_only_patch(self):
        """Test PATCH method to update only the title"""
        campaign = Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Original Title",
            description="Original Description",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )

        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-detail', args=[campaign.id])

        # Update only title
        update_data = {"title": "New Title"}
        response = self.client.patch(url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        campaign.refresh_from_db()
        self.assertEqual(campaign.title, "New Title")
        self.assertEqual(campaign.description, "Original Description")  # Unchanged

    def test_update_campaign_description_only_patch(self):
        """Test PATCH method to update only the description"""
        campaign = Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Original Title",
            description="Original Description",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )

        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-detail', args=[campaign.id])

        # Update only description
        update_data = {"description": "New Description"}
        response = self.client.patch(url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        campaign.refresh_from_db()
        self.assertEqual(campaign.title, "Original Title")  # Unchanged
        self.assertEqual(campaign.description, "New Description")

    def test_update_campaign_full_put(self):
        """Test PUT method to update title and description"""
        campaign = Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Original Title",
            description="Original Description",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )

        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-detail', args=[campaign.id])

        # Full update with PUT
        update_data = {
            "title": "Completely New Title",
            "description": "Completely New Description"
        }
        response = self.client.put(url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        campaign.refresh_from_db()
        self.assertEqual(campaign.title, "Completely New Title")
        self.assertEqual(campaign.description, "Completely New Description")
        # Dates should remain unchanged
        self.assertEqual(campaign.start_date, date.today() + timedelta(days=1))
        self.assertEqual(campaign.end_date, date.today() + timedelta(days=30))

    def test_access_other_user_campaign_forbidden(self):
        """Test that HR managers cannot access other managers' campaigns"""
        campaign = Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Campaign HR1",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )
        
        # HR Manager 2 tries to access HR Manager 1's campaign
        self.authenticate_user(self.hr_manager2)
        url = reverse('campaign-detail', args=[campaign.id])
        
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_campaign_success(self):
        """Test successful campaign deletion"""
        campaign = Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Campaign to Delete",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )
        
        self.authenticate_user(self.hr_manager1)
        url = reverse('campaign-detail', args=[campaign.id])
        
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Campaign.objects.count(), 0)

    def test_delete_other_user_campaign_forbidden(self):
        """Test that HR managers cannot delete other managers' campaigns"""
        campaign = Campaign.objects.create(
            hr_manager=self.hr_manager1,
            title="Campaign HR1",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )
        
        # HR Manager 2 tries to delete HR Manager 1's campaign
        self.authenticate_user(self.hr_manager2)
        url = reverse('campaign-detail', args=[campaign.id])
        
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(Campaign.objects.count(), 1)  # Campaign still exists


class CampaignPermissionsTest(APITestCase):
    """Test cases for Campaign permissions and security"""
    
    def setUp(self):
        self.hr_manager = HRManager.objects.create(
            name='Test HR Manager',
            email='<EMAIL>',
            password_hash=make_password('password123'),
            company_name='Test Company'
        )
        
        self.campaign = Campaign.objects.create(
            hr_manager=self.hr_manager,
            title="Test Campaign",
            start_date=date.today() + timedelta(days=1),
            end_date=date.today() + timedelta(days=30)
        )

    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated users cannot access campaigns"""
        url = reverse('campaign-list')
        response = self.client.get(url)
        # Peut être 401 ou 403 selon la configuration des permissions
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_authenticated_access_allowed(self):
        """Test that authenticated users can access their campaigns"""
        self.client.force_authenticate(user=self.hr_manager)
        url = reverse('campaign-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
