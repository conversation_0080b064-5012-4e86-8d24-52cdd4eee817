# Generated by Django 5.2.4 on 2025-07-24 22:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0004_rename_hr_manager_field'),
        ('employees', '0003_remove_employee_employees_e_email_8f5bbc_idx_and_more'),
        ('matching', '0002_alter_campaignmatchingcriteria_rule'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='campaignmatchingcriteria',
            options={'ordering': ['created_at']},
        ),
        migrations.AlterModelOptions(
            name='employeepair',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddField(
            model_name='campaignmatchingcriteria',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=0),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='campaignmatchingcriteria',
            name='created_by',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='campaignmatchingcriteria',
            name='is_locked',
            field=models.<PERSON><PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name='employeepair',
            name='created_by',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='employeepair',
            name='email_error_message',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='employeepair',
            name='email_sent_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeepair',
            name='email_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('failed', 'Failed'), ('bounced', 'Bounced')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='employeepair',
            name='matching_criteria_snapshot',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='campaignmatchingcriteria',
            unique_together={('campaign', 'attribute_key')},
        ),
        migrations.AddIndex(
            model_name='campaignmatchingcriteria',
            index=models.Index(fields=['campaign', 'created_at'], name='matching_ca_campaig_8927e9_idx'),
        ),
        migrations.AddIndex(
            model_name='employeepair',
            index=models.Index(fields=['campaign', 'created_at'], name='matching_em_campaig_04b39e_idx'),
        ),
        migrations.AddIndex(
            model_name='employeepair',
            index=models.Index(fields=['email_status'], name='matching_em_email_s_3ab92e_idx'),
        ),
        migrations.AddIndex(
            model_name='employeepair',
            index=models.Index(fields=['employee1', 'employee2'], name='matching_em_employe_8df1cb_idx'),
        ),
        migrations.AddConstraint(
            model_name='employeepair',
            constraint=models.UniqueConstraint(fields=('campaign', 'employee1', 'employee2'), name='unique_pair_forward'),
        ),
        migrations.AddConstraint(
            model_name='employeepair',
            constraint=models.UniqueConstraint(fields=('campaign', 'employee2', 'employee1'), name='unique_pair_reverse'),
        ),
    ]
