"""
Django management command to test Excel import functionality.

Usage: python manage.py test_excel_import
"""

import os
import tempfile
from datetime import date, timedelta
from django.core.management.base import BaseCommand
from django.core.files.uploadedfile import SimpleUploadedFile
import pandas as pd

from users.models import HRManager
from campaigns.models import Campaign
from employees.models import Employee, EmployeeAttribute
from employees.services import ExcelProcessingService


class Command(BaseCommand):
    help = 'Test Excel import functionality and data storage'

    def handle(self, *args, **options):
        """Test Excel import functionality"""
        self.stdout.write("🧪 Testing Excel Import Functionality")
        self.stdout.write("=" * 50)
        
        # Clean up any existing test data
        Employee.objects.filter(email__contains='testexcel').delete()
        Campaign.objects.filter(title__startswith='Excel Test').delete()
        HRManager.objects.filter(email__startswith='excelhr').delete()
        
        try:
            # Create test HR manager and campaign
            hr_manager = HRManager.objects.create(
                name="Excel Test HR Manager",
                email="<EMAIL>",
                password_hash="dummy_hash",
                company_name="Excel Test Company"
            )
            
            campaign = Campaign.objects.create(
                title="Excel Test Campaign",
                description="Test campaign for Excel import",
                start_date=date.today() + timedelta(days=1),
                end_date=date.today() + timedelta(days=31),
                hr_manager=hr_manager
            )
            self.stdout.write(self.style.SUCCESS("✅ Created test campaign"))
            
            # Test 1: Create and test Excel file with various data types
            excel_data = {
                'Full Name': ['John Doe', 'Jane Smith', 'Mike Johnson'],
                'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'Arrival Date': ['2024-01-15', '2024-02-01', '2024-01-20'],
                'Department': ['Engineering', 'Marketing', 'Sales'],
                'Position': ['Software Developer', 'Marketing Manager', 'Sales Representative'],
                'Salary': [75000, 65000, 55000],
                'Location': ['New York', 'Los Angeles', 'Chicago'],
                'Manager': ['Alice Brown', 'Bob Wilson', 'Carol Davis']
            }
            
            # Create Excel file
            df = pd.DataFrame(excel_data)
            excel_file = self._create_excel_file(df)
            
            # Test Excel import
            service = ExcelProcessingService(campaign.id, replace_existing=False)
            result = service.process_excel_file(excel_file)
            
            if result['success']:
                self.stdout.write(self.style.SUCCESS(f"✅ Test 1 PASSED: Excel import successful"))
                self.stdout.write(f"   - Total rows: {result['total_rows']}")
                self.stdout.write(f"   - Processed rows: {result['processed_rows']}")
                self.stdout.write(f"   - Created employees: {result['created_employees']}")
                self.stdout.write(f"   - Errors: {len(result['errors'])}")
            else:
                self.stdout.write(self.style.ERROR(f"❌ Test 1 FAILED: {result['error']}"))
                return
            
            # Test 2: Verify data storage in database
            employees = Employee.objects.filter(campaign=campaign)
            if employees.count() == 3:
                self.stdout.write(self.style.SUCCESS("✅ Test 2 PASSED: All employees stored in database"))
            else:
                self.stdout.write(self.style.ERROR(f"❌ Test 2 FAILED: Expected 3 employees, found {employees.count()}"))
                return
            
            # Test 3: Verify core fields are correctly mapped
            john = employees.filter(email='<EMAIL>').first()
            if john and john.name == 'John Doe':
                self.stdout.write(self.style.SUCCESS("✅ Test 3 PASSED: Core fields correctly mapped"))
            else:
                self.stdout.write(self.style.ERROR("❌ Test 3 FAILED: Core field mapping issue"))
                return
            
            # Test 4: Verify additional attributes are stored
            john_attributes = EmployeeAttribute.objects.filter(employee=john)
            expected_attributes = ['department', 'position', 'salary', 'location', 'manager']
            
            stored_attributes = {attr.attribute_key for attr in john_attributes}
            missing_attributes = set(expected_attributes) - stored_attributes
            
            if not missing_attributes:
                self.stdout.write(self.style.SUCCESS("✅ Test 4 PASSED: All additional attributes stored"))
                for attr in john_attributes:
                    self.stdout.write(f"   - {attr.attribute_key}: {attr.attribute_value}")
            else:
                self.stdout.write(self.style.ERROR(f"❌ Test 4 FAILED: Missing attributes: {missing_attributes}"))
                return
            
            # Test 5: Verify attribute values are correct
            dept_attr = john_attributes.filter(attribute_key='department').first()
            if dept_attr and dept_attr.attribute_value == 'Engineering':
                self.stdout.write(self.style.SUCCESS("✅ Test 5 PASSED: Attribute values correctly stored"))
            else:
                self.stdout.write(self.style.ERROR("❌ Test 5 FAILED: Attribute value mismatch"))
                return
            
            # Test 6: Test different date formats
            date_test_data = {
                'Name': ['Date Test 1', 'Date Test 2', 'Date Test 3'],
                'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'Arrival Date': ['2024-01-15', '15/01/2024', '01/15/2024']  # Different formats
            }
            
            df_dates = pd.DataFrame(date_test_data)
            excel_file_dates = self._create_excel_file(df_dates)
            
            # Create new campaign for date test
            campaign2 = Campaign.objects.create(
                title="Excel Date Test Campaign",
                description="Test campaign for date formats",
                start_date=date.today() + timedelta(days=1),
                end_date=date.today() + timedelta(days=31),
                hr_manager=hr_manager
            )
            
            service2 = ExcelProcessingService(campaign2.id, replace_existing=False)
            result2 = service2.process_excel_file(excel_file_dates)
            
            if result2['success'] and result2['created_employees'] == 3:
                self.stdout.write(self.style.SUCCESS("✅ Test 6 PASSED: Different date formats handled correctly"))
            else:
                self.stdout.write(self.style.ERROR(f"❌ Test 6 FAILED: Date format handling issue"))
                return
            
            # Test 7: Test duplicate prevention within same campaign
            duplicate_data = {
                'Name': ['John Duplicate'],
                'Email': ['<EMAIL>'],  # Same email as first test
                'Arrival Date': ['2024-03-01']
            }
            
            df_duplicate = pd.DataFrame(duplicate_data)
            excel_file_duplicate = self._create_excel_file(df_duplicate)
            
            service3 = ExcelProcessingService(campaign.id, replace_existing=False)  # Same campaign
            result3 = service3.process_excel_file(excel_file_duplicate)
            
            if not result3['success'] and len(result3['errors']) > 0:
                self.stdout.write(self.style.SUCCESS("✅ Test 7 PASSED: Duplicate prevention working"))
            else:
                self.stdout.write(self.style.ERROR("❌ Test 7 FAILED: Duplicate not prevented"))
                return
            
            # Test 8: Test cross-campaign duplication (should work)
            service4 = ExcelProcessingService(campaign2.id, replace_existing=False)  # Different campaign
            result4 = service4.process_excel_file(excel_file_duplicate)
            
            if result4['success'] and result4['created_employees'] == 1:
                self.stdout.write(self.style.SUCCESS("✅ Test 8 PASSED: Cross-campaign duplication allowed"))
            else:
                self.stdout.write(self.style.ERROR("❌ Test 8 FAILED: Cross-campaign duplication blocked"))
                return
            
            self.stdout.write("\n🎉 ALL EXCEL IMPORT TESTS PASSED!")
            self.stdout.write(self.style.SUCCESS("✅ Excel files are correctly read and parsed"))
            self.stdout.write(self.style.SUCCESS("✅ Column mapping works for various formats"))
            self.stdout.write(self.style.SUCCESS("✅ Core employee data is properly stored"))
            self.stdout.write(self.style.SUCCESS("✅ Additional attributes are correctly extracted and stored"))
            self.stdout.write(self.style.SUCCESS("✅ Date formats are handled properly"))
            self.stdout.write(self.style.SUCCESS("✅ Duplicate prevention works within campaigns"))
            self.stdout.write(self.style.SUCCESS("✅ Cross-campaign duplication is allowed"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed with error: {str(e)}"))
            raise
        
        finally:
            # Clean up test data
            Employee.objects.filter(email__contains='testexcel').delete()
            Campaign.objects.filter(title__startswith='Excel Test').delete()
            HRManager.objects.filter(email__startswith='excelhr').delete()
            self.stdout.write("\n🧹 Cleaned up test data")
    
    def _create_excel_file(self, df):
        """Create a temporary Excel file from DataFrame"""
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
            df.to_excel(tmp_file.name, index=False, engine='openpyxl')
            
            # Read the file back as bytes for upload
            with open(tmp_file.name, 'rb') as f:
                file_content = f.read()
            
            # Clean up temp file
            os.unlink(tmp_file.name)
            
            # Create uploaded file object
            return SimpleUploadedFile(
                name="test_employees.xlsx",
                content=file_content,
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
