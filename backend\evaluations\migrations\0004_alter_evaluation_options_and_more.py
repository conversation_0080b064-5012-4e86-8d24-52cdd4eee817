# Generated by Django 5.2.4 on 2025-07-31 22:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0005_alter_employee_options_and_more'),
        ('evaluations', '0003_alter_evaluation_comment_alter_evaluation_rating'),
        ('matching', '0004_add_missing_fields'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='evaluation',
            options={'ordering': ['-submitted_at']},
        ),
        migrations.AddIndex(
            model_name='evaluation',
            index=models.Index(fields=['employee_pair', 'used'], name='evaluations_employe_cd0353_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluation',
            index=models.Index(fields=['employee', 'submitted_at'], name='evaluations_employe_938277_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluation',
            index=models.Index(fields=['token'], name='evaluations_token_44179f_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluation',
            index=models.Index(fields=['used', 'submitted_at'], name='evaluations_used_0cbbc8_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluation',
            index=models.Index(fields=['rating'], name='evaluations_rating_660477_idx'),
        ),
    ]
