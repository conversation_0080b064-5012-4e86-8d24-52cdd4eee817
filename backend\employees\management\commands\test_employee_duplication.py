"""
Django management command to test employee duplication functionality.

Usage: python manage.py test_employee_duplication
"""

from django.core.management.base import BaseCommand
from django.db import IntegrityError
from datetime import date, timedelta

from users.models import HRManager
from campaigns.models import Campaign
from employees.models import Employee


class Command(BaseCommand):
    help = 'Test employee duplication functionality across campaigns'

    def handle(self, *args, **options):
        """Test employee duplication across campaigns"""
        self.stdout.write("🧪 Testing Employee Duplication Functionality")
        self.stdout.write("=" * 50)
        
        # Clean up any existing test data
        Employee.objects.filter(email__in=['<EMAIL>', '<EMAIL>']).delete()
        Campaign.objects.filter(title__startswith='Test Campaign').delete()
        HRManager.objects.filter(email__startswith='testhr').delete()
        
        try:
            # Create test HR manager
            hr_manager = HRManager.objects.create(
                name="Test HR Manager",
                email="<EMAIL>",
                password_hash="dummy_hash",
                company_name="Test Company"
            )
            self.stdout.write(self.style.SUCCESS("✅ Created test HR manager"))
            
            # Create two test campaigns
            campaign1 = Campaign.objects.create(
                title="Test Campaign 1",
                description="First test campaign",
                start_date=date.today() + timedelta(days=1),
                end_date=date.today() + timedelta(days=31),
                hr_manager=hr_manager
            )
            
            campaign2 = Campaign.objects.create(
                title="Test Campaign 2", 
                description="Second test campaign",
                start_date=date.today() + timedelta(days=1),
                end_date=date.today() + timedelta(days=31),
                hr_manager=hr_manager
            )
            self.stdout.write(self.style.SUCCESS("✅ Created two test campaigns"))
            
            # Test 1: Create employee in first campaign
            employee1 = Employee.objects.create(
                name="John Doe",
                email="<EMAIL>",
                arrival_date=date.today(),
                campaign=campaign1
            )
            self.stdout.write(self.style.SUCCESS("✅ Test 1 PASSED: Created employee in Campaign 1"))
            
            # Test 2: Create same employee (same email) in second campaign - should work
            employee2 = Employee.objects.create(
                name="John Doe",
                email="<EMAIL>",  # Same email as employee1
                arrival_date=date.today(),
                campaign=campaign2
            )
            self.stdout.write(self.style.SUCCESS("✅ Test 2 PASSED: Created same employee in Campaign 2 (different campaigns allow duplicates)"))
            
            # Test 3: Try to create duplicate in same campaign - should fail
            try:
                employee3 = Employee.objects.create(
                    name="John Smith",  # Different name
                    email="<EMAIL>",  # Same email as employee1
                    arrival_date=date.today(),
                    campaign=campaign1  # Same campaign as employee1
                )
                self.stdout.write(self.style.ERROR("❌ Test 3 FAILED: Should not allow duplicate email in same campaign"))
                return
            except IntegrityError:
                self.stdout.write(self.style.SUCCESS("✅ Test 3 PASSED: Correctly prevented duplicate email in same campaign"))
            
            # Test 4: Verify we have 2 employees with same email in different campaigns
            john_employees = Employee.objects.filter(email="<EMAIL>")
            if john_employees.count() == 2:
                self.stdout.write(self.style.SUCCESS("✅ Test 4 PASSED: Found 2 employees with same email in different campaigns"))
                self.stdout.write(f"   - Employee 1: {john_employees[0].name} in {john_employees[0].campaign.title}")
                self.stdout.write(f"   - Employee 2: {john_employees[1].name} in {john_employees[1].campaign.title}")
            else:
                self.stdout.write(self.style.ERROR(f"❌ Test 4 FAILED: Expected 2 employees, found {john_employees.count()}"))
                return
            
            # Test 5: Create different employee in same campaign - should work
            employee4 = Employee.objects.create(
                name="Jane Smith",
                email="<EMAIL>",  # Different email
                arrival_date=date.today(),
                campaign=campaign1  # Same campaign as employee1
            )
            self.stdout.write(self.style.SUCCESS("✅ Test 5 PASSED: Created different employee in same campaign"))
            
            self.stdout.write("\n🎉 ALL TESTS PASSED!")
            self.stdout.write(self.style.SUCCESS("✅ Employees can be duplicated across different campaigns"))
            self.stdout.write(self.style.SUCCESS("✅ Employees cannot be duplicated within the same campaign"))
            self.stdout.write(self.style.SUCCESS("✅ Database constraints are working correctly"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Test failed with error: {str(e)}"))
            raise
        
        finally:
            # Clean up test data
            Employee.objects.filter(email__in=['<EMAIL>', '<EMAIL>']).delete()
            Campaign.objects.filter(title__startswith='Test Campaign').delete()
            HRManager.objects.filter(email__startswith='testhr').delete()
            self.stdout.write("\n🧹 Cleaned up test data")
