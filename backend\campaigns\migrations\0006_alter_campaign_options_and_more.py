# Generated by Django 5.2.4 on 2025-07-31 22:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0005_campaignworkflowlog_campaignworkflowstate'),
        ('users', '0002_passwordresettoken'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='campaign',
            options={'ordering': ['-created_at']},
        ),
        migrations.AddIndex(
            model_name='campaign',
            index=models.Index(fields=['hr_manager', 'created_at'], name='campaigns_c_hr_mana_55d30f_idx'),
        ),
        migrations.AddIndex(
            model_name='campaign',
            index=models.Index(fields=['start_date', 'end_date'], name='campaigns_c_start_d_996cc8_idx'),
        ),
        migrations.AddIndex(
            model_name='campaign',
            index=models.Index(fields=['hr_manager', 'start_date'], name='campaigns_c_hr_mana_ae22b9_idx'),
        ),
        migrations.AddIndex(
            model_name='campaign',
            index=models.Index(fields=['created_at'], name='campaigns_c_created_eda2da_idx'),
        ),
    ]
