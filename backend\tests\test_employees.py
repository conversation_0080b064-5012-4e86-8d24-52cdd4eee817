# tests/test_employees.py
"""
Tests for employees app functionality

This module contains comprehensive tests for the employees app including:
- Model tests for Employee and EmployeeAttribute
- API tests for CRUD operations
- Excel upload and processing tests
- Permission and security tests
"""

import os
import tempfile
from datetime import date, timedelta
from io import BytesIO

import pandas as pd
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from campaigns.models import Campaign
from employees.models import Employee, EmployeeAttribute
from employees.services import ExcelProcessingService
from users.models import HRManager
from .utils import TestDataFactory, APITestMixin


class EmployeeModelTest(TestCase):
    """Test Employee model functionality"""
    
    def setUp(self):
        self.hr_manager = TestDataFactory.create_hr_manager()
        self.campaign = TestDataFactory.create_campaign(self.hr_manager)
    
    def test_employee_creation(self):
        """Test basic employee creation"""
        employee = Employee.objects.create(
            name="<PERSON>",
            email="<EMAIL>",
            arrival_date=date.today(),
            campaign=self.campaign
        )
        
        self.assertEqual(employee.name, "<PERSON>")
        self.assertEqual(employee.email, "<EMAIL>")
        self.assertEqual(employee.campaign, self.campaign)
        self.assertIsNotNone(employee.created_at)
        self.assertIsNotNone(employee.updated_at)
    
    def test_employee_str_representation(self):
        """Test employee string representation"""
        employee = TestDataFactory.create_employee()
        self.assertEqual(str(employee), employee.name)
    
    def test_get_attributes_dict(self):
        """Test getting employee attributes as dictionary"""
        employee = TestDataFactory.create_employee()
        
        # Create some attributes
        EmployeeAttribute.objects.create(
            employee=employee,
            campaign=self.campaign,
            attribute_key="department",
            attribute_value="Engineering"
        )
        EmployeeAttribute.objects.create(
            employee=employee,
            campaign=self.campaign,
            attribute_key="level",
            attribute_value="Senior"
        )
        
        attributes = employee.get_attributes_dict()
        expected = {
            "department": "Engineering",
            "level": "Senior"
        }
        self.assertEqual(attributes, expected)


class EmployeeAttributeModelTest(TestCase):
    """Test EmployeeAttribute model functionality"""
    
    def setUp(self):
        self.hr_manager = TestDataFactory.create_hr_manager()
        self.campaign = TestDataFactory.create_campaign(self.hr_manager)
        self.employee = TestDataFactory.create_employee()
    
    def test_attribute_creation(self):
        """Test basic attribute creation"""
        attribute = EmployeeAttribute.objects.create(
            employee=self.employee,
            campaign=self.campaign,
            attribute_key="department",
            attribute_value="Engineering"
        )
        
        self.assertEqual(attribute.employee, self.employee)
        self.assertEqual(attribute.campaign, self.campaign)
        self.assertEqual(attribute.attribute_key, "department")
        self.assertEqual(attribute.attribute_value, "Engineering")
    
    def test_attribute_str_representation(self):
        """Test attribute string representation"""
        attribute = EmployeeAttribute.objects.create(
            employee=self.employee,
            campaign=self.campaign,
            attribute_key="department",
            attribute_value="Engineering"
        )
        
        expected = "department: Engineering"
        self.assertEqual(str(attribute), expected)


class EmployeeAPITest(APITestCase, APITestMixin):
    """Test Employee API endpoints"""
    
    def setUp(self):
        self.hr_manager = TestDataFactory.create_hr_manager()
        self.campaign = TestDataFactory.create_campaign(self.hr_manager)
        self.authenticate_user(self.hr_manager)
    
    def test_list_employees(self):
        """Test listing employees"""
        # Create test employees
        employee1 = TestDataFactory.create_employee(name="John Doe", email="<EMAIL>")
        employee2 = TestDataFactory.create_employee(name="Jane Smith", email="<EMAIL>")
        
        url = reverse('employee-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_create_employee(self):
        """Test creating an employee"""
        url = reverse('employee-list')
        data = {
            'name': 'New Employee',
            'email': '<EMAIL>',
            'arrival_date': date.today().isoformat(),
            'campaign': self.campaign.id,
            'attributes': {
                'department': 'Engineering',
                'level': 'Junior'
            }
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Employee.objects.count(), 1)
        
        employee = Employee.objects.first()
        self.assertEqual(employee.name, 'New Employee')
        self.assertEqual(employee.email, '<EMAIL>')
        self.assertEqual(employee.campaign, self.campaign)
        
        # Check attributes were created
        attributes = employee.get_attributes_dict()
        self.assertEqual(attributes['department'], 'Engineering')
        self.assertEqual(attributes['level'], 'Junior')
    
    def test_get_employee_detail(self):
        """Test getting employee details"""
        employee = TestDataFactory.create_employee()
        
        url = reverse('employee-detail', args=[employee.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], employee.name)
        self.assertEqual(response.data['email'], employee.email)
    
    def test_update_employee(self):
        """Test updating an employee"""
        employee = TestDataFactory.create_employee()
        
        url = reverse('employee-detail', args=[employee.id])
        data = {
            'name': 'Updated Name',
            'email': employee.email,
            'arrival_date': employee.arrival_date.isoformat()
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        employee.refresh_from_db()
        self.assertEqual(employee.name, 'Updated Name')
    
    def test_delete_employee(self):
        """Test deleting an employee"""
        employee = TestDataFactory.create_employee()
        
        url = reverse('employee-detail', args=[employee.id])
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Employee.objects.count(), 0)
    
    def test_filter_employees_by_campaign(self):
        """Test filtering employees by campaign"""
        # Create employees for different campaigns
        campaign2 = TestDataFactory.create_campaign(self.hr_manager, title="Campaign 2")
        
        employee1 = TestDataFactory.create_employee(name="Employee 1", email="<EMAIL>")
        employee1.campaign = self.campaign
        employee1.save()
        
        employee2 = TestDataFactory.create_employee(name="Employee 2", email="<EMAIL>")
        employee2.campaign = campaign2
        employee2.save()
        
        url = reverse('employee-list')
        response = self.client.get(url, {'campaign': self.campaign.id})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Employee 1')
    
    def test_search_employees(self):
        """Test searching employees by name or email"""
        employee1 = TestDataFactory.create_employee(name="John Doe", email="<EMAIL>")
        employee2 = TestDataFactory.create_employee(name="Jane Smith", email="<EMAIL>")
        
        url = reverse('employee-list')
        
        # Search by name
        response = self.client.get(url, {'search': 'John'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'John Doe')
        
        # Search by email
        response = self.client.get(url, {'search': '<EMAIL>'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Jane Smith')


class ExcelProcessingTest(TestCase):
    """Test Excel file processing functionality"""
    
    def setUp(self):
        self.hr_manager = TestDataFactory.create_hr_manager()
        self.campaign = TestDataFactory.create_campaign(self.hr_manager)
        self.service = ExcelProcessingService(self.campaign.id)
    
    def create_test_excel_file(self, data):
        """Helper method to create test Excel file"""
        df = pd.DataFrame(data)
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        df.to_excel(temp_file.name, index=False)
        temp_file.close()
        
        return temp_file.name
    
    def test_process_valid_excel_file(self):
        """Test processing a valid Excel file"""
        data = [
            {'name': 'John Doe', 'email': '<EMAIL>', 'arrival_date': '2024-01-15', 'department': 'Engineering'},
            {'name': 'Jane Smith', 'email': '<EMAIL>', 'arrival_date': '2024-01-20', 'department': 'Marketing'},
        ]
        
        excel_file = self.create_test_excel_file(data)
        
        try:
            with open(excel_file, 'rb') as f:
                result = self.service.process_excel_file(f)
            
            self.assertTrue(result['success'])
            self.assertEqual(result['total_rows'], 2)
            self.assertEqual(result['processed_rows'], 2)
            self.assertEqual(result['created_employees'], 2)
            self.assertEqual(len(result['errors']), 0)
            
            # Check employees were created
            self.assertEqual(Employee.objects.count(), 2)
            
            # Check attributes were created
            john = Employee.objects.get(email='<EMAIL>')
            self.assertEqual(john.get_attributes_dict()['department'], 'Engineering')
            
        finally:
            os.unlink(excel_file)
    
    def test_process_excel_with_missing_required_fields(self):
        """Test processing Excel file with missing required fields"""
        data = [
            {'name': 'John Doe', 'department': 'Engineering'},  # Missing email
            {'email': '<EMAIL>', 'department': 'Marketing'},  # Missing name
        ]
        
        excel_file = self.create_test_excel_file(data)
        
        try:
            with open(excel_file, 'rb') as f:
                result = self.service.process_excel_file(f)
            
            self.assertTrue(result['success'])  # Process continues despite errors
            self.assertEqual(result['total_rows'], 2)
            self.assertEqual(result['processed_rows'], 2)
            self.assertEqual(result['created_employees'], 0)  # No employees created due to errors
            self.assertEqual(len(result['errors']), 2)
            
        finally:
            os.unlink(excel_file)
    
    def test_process_excel_with_duplicate_emails(self):
        """Test processing Excel file with duplicate emails"""
        # Create existing employee
        TestDataFactory.create_employee(email='<EMAIL>')
        
        data = [
            {'name': 'John Doe', 'email': '<EMAIL>', 'department': 'Engineering'},
            {'name': 'Jane Smith', 'email': '<EMAIL>', 'department': 'Marketing'},
        ]
        
        excel_file = self.create_test_excel_file(data)
        
        try:
            with open(excel_file, 'rb') as f:
                result = self.service.process_excel_file(f)
            
            self.assertTrue(result['success'])
            self.assertEqual(result['created_employees'], 1)  # Only Jane created
            self.assertEqual(len(result['errors']), 1)  # John's email already exists
            
        finally:
            os.unlink(excel_file)


class ExcelUploadAPITest(APITestCase, APITestMixin):
    """Test Excel upload API endpoint"""
    
    def setUp(self):
        self.hr_manager = TestDataFactory.create_hr_manager()
        self.campaign = TestDataFactory.create_campaign(self.hr_manager)
        self.authenticate_user(self.hr_manager)
    
    def create_test_excel_file_content(self, data):
        """Helper method to create Excel file content in memory"""
        df = pd.DataFrame(data)
        buffer = BytesIO()
        df.to_excel(buffer, index=False)
        buffer.seek(0)
        return buffer
    
    def test_upload_valid_excel_file(self):
        """Test uploading a valid Excel file"""
        data = [
            {'name': 'John Doe', 'email': '<EMAIL>', 'arrival_date': '2024-01-15'},
            {'name': 'Jane Smith', 'email': '<EMAIL>', 'arrival_date': '2024-01-20'},
        ]
        
        excel_content = self.create_test_excel_file_content(data)
        
        url = reverse('employee-upload-excel')
        response = self.client.post(url, {
            'file': excel_content,
            'campaign_id': self.campaign.id
        }, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['created_employees'], 2)
        self.assertEqual(Employee.objects.count(), 2)
    
    def test_upload_invalid_file_format(self):
        """Test uploading invalid file format"""
        url = reverse('employee-upload-excel')
        
        # Create a text file instead of Excel
        text_content = BytesIO(b"This is not an Excel file")
        
        response = self.client.post(url, {
            'file': text_content,
            'campaign_id': self.campaign.id
        }, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_upload_without_campaign_id(self):
        """Test uploading without campaign_id"""
        data = [{'name': 'John Doe', 'email': '<EMAIL>'}]
        excel_content = self.create_test_excel_file_content(data)
        
        url = reverse('employee-upload-excel')
        response = self.client.post(url, {
            'file': excel_content
        }, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class CampaignEmployeesAPITest(APITestCase, APITestMixin):
    """Test campaign-specific employee endpoints"""
    
    def setUp(self):
        self.hr_manager = TestDataFactory.create_hr_manager()
        self.campaign = TestDataFactory.create_campaign(self.hr_manager)
        self.authenticate_user(self.hr_manager)
    
    def test_get_campaign_employees(self):
        """Test getting employees for a specific campaign"""
        # Create employees for this campaign
        employee1 = TestDataFactory.create_employee(name="Employee 1", email="<EMAIL>")
        employee1.campaign = self.campaign
        employee1.save()
        
        employee2 = TestDataFactory.create_employee(name="Employee 2", email="<EMAIL>")
        employee2.campaign = self.campaign
        employee2.save()
        
        # Create employee for different campaign
        other_campaign = TestDataFactory.create_campaign(self.hr_manager, title="Other Campaign")
        employee3 = TestDataFactory.create_employee(name="Employee 3", email="<EMAIL>")
        employee3.campaign = other_campaign
        employee3.save()
        
        url = reverse('campaign-employees', args=[self.campaign.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 2)
        self.assertEqual(len(response.data['employees']), 2)
        self.assertEqual(response.data['campaign']['id'], self.campaign.id)
        self.assertEqual(response.data['campaign']['title'], self.campaign.title)
