# Generated by Django 5.2.4 on 2025-07-31 22:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0006_alter_campaign_options_and_more'),
        ('employees', '0004_alter_employee_email_alter_employee_unique_together'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='employee',
            options={'ordering': ['name']},
        ),
        migrations.AlterUniqueTogether(
            name='employeeattribute',
            unique_together={('employee', 'attribute_key')},
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['campaign', 'name'], name='employees_e_campaig_3c2ebc_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['campaign', 'email'], name='employees_e_campaig_f146da_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['campaign', 'arrival_date'], name='employees_e_campaig_c974b9_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['email'], name='employees_e_email_8f5bbc_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['name'], name='employees_e_name_95200c_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeattribute',
            index=models.Index(fields=['employee', 'attribute_key'], name='employees_e_employe_b19e64_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeattribute',
            index=models.Index(fields=['campaign', 'attribute_key'], name='employees_e_campaig_1d30eb_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeattribute',
            index=models.Index(fields=['attribute_key', 'attribute_value'], name='employees_e_attribu_3eacbb_idx'),
        ),
        migrations.AddIndex(
            model_name='employeeattribute',
            index=models.Index(fields=['employee'], name='employees_e_employe_a98d38_idx'),
        ),
    ]
