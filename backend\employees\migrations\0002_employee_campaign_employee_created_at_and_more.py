# Generated by Django 5.2.4 on 2025-07-23 19:35

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0004_rename_hr_manager_field'),
        ('employees', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='employee',
            name='campaign',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='campaigns.campaign'),
        ),
        migrations.AddField(
            model_name='employee',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='employee',
            name='import_batch_id',
            field=models.CharField(blank=True, help_text='Batch ID for tracking Excel imports', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='imported_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='employee',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['email'], name='employees_e_email_8f5bbc_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['campaign'], name='employees_e_campaig_9b4e17_idx'),
        ),
        migrations.AddIndex(
            model_name='employee',
            index=models.Index(fields=['import_batch_id'], name='employees_e_import__4ebb63_idx'),
        ),
    ]
